//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/browse_category_page.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/bootstrap/helpers.dart';

class CategoriesPage extends NyStatefulWidget {
  static RouteView path = ("/categories", (_) => CategoriesPage());

  CategoriesPage({super.key}) : super(child: () => _CategoriesPageState());
}

class _CategoriesPageState extends NyPage<CategoriesPage> {
  List<MyProductCategory> _categories = [];
  List<MyProductCategory> _filteredCategories = [];
  final TextEditingController _searchController = TextEditingController();

  @override
  get init => () async {
    await _loadCategories();
  };

  _loadCategories() async {
    try {
      _categories = await WooCommerceService().getProductCategories(
        parent: 0,
        perPage: 50,
        hideEmpty: true,
      );

      // Sort categories by menu order
      _categories.sort((category1, category2) =>
          category1.getSafeMenuOrder().compareTo(category2.getSafeMenuOrder()));

      _filteredCategories = _categories;
      setState(() {});
    } catch (e) {
      NyLogger.error("Error loading categories: $e");
    }
  }

  void _filterCategories(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCategories = _categories;
      } else {
        _filteredCategories = _categories
            .where((category) =>
                category.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              title: Text(
                'الفئات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).appBarTheme.titleTextStyle?.color,
                ),
              ),
              centerTitle: true,
              automaticallyImplyLeading: false,
              floating: true,
              snap: true,
              pinned: false,
              expandedHeight: 120.0,
              backgroundColor: Theme.of(context).appBarTheme.backgroundColor?.withValues(
                alpha: innerBoxIsScrolled ? 0.95 : 1.0,
              ),
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context).appBarTheme.backgroundColor ?? Colors.white,
                        (Theme.of(context).appBarTheme.backgroundColor ?? Colors.white).withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: SafeAreaWidget(
        child: _categories.isEmpty
            ? Center(
                child: CircularProgressIndicator(
                  color: ThemeColor.get(context).brandPrimary, // Brand color
                ),
              )
            : Column(
                children: [
                  // Search Bar
                  Container(
                    margin: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      onChanged: _filterCategories,
                      textAlign: TextAlign.right,
                      decoration: InputDecoration(
                        hintText: 'البحث في الفئات...',
                        hintStyle: TextStyle(
                          color: ThemeColor.get(context).textMuted,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: ThemeColor.get(context).brandPrimary,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                    ),
                  ),

                  // Categories Header
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'عرض الكل',
                          style: TextStyle(
                            color: ThemeColor.get(context).brandPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'الفئات (${_filteredCategories.length})',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 16),

                  // Categories Grid
                  Expanded(
                    child: _filteredCategories.isEmpty
                        ? _buildEmptyState()
                        : GridView.builder(
                            padding: EdgeInsets.all(20),
                            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: DesignConstants.getResponsiveGridCount(context),
                              crossAxisSpacing: 20,
                              mainAxisSpacing: 20,
                              childAspectRatio: DesignConstants.getResponsiveAspectRatio(context),
                            ),
                            itemCount: _filteredCategories.length,
                            itemBuilder: (context, index) {
                              MyProductCategory category = _filteredCategories[index];
                              return _buildModernCategoryCard(category);
                            },
                          ),
                  ),
                ],
              ),
        ),
      ),
    );
  }

  Widget _buildModernCategoryCard(MyProductCategory category) {
    return InkWell(
      onTap: () {
        routeTo(BrowseCategoryPage.path, data: category);
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: Offset(0, 8),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 6,
              offset: Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              // Background Image or Enhanced Gradient
              Positioned.fill(
                child: category.image?.src?.isNotEmpty == true
                    ? CachedImageWidget(
                        image: category.image!.src!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      )
                    : _buildEnhancedCategoryPlaceholder(category),
              ),

              // Modern Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                        Colors.black.withValues(alpha: 0.8),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                ),
              ),

              // Category Information with Modern Layout
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        category.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 2),
                              blurRadius: 4,
                              color: Colors.black.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (category.count > 0)
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '${category.count} منتج',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.white,
                              size: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedCategoryPlaceholder(MyProductCategory category) {
    // Create a modern gradient placeholder with enhanced visuals
    List<Color> gradientColors = _getEnhancedCategoryGradient(category.name);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
          stops: [0.0, 0.7, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.topRight,
                  radius: 1.5,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // Icon
          Center(
            child: Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                _getCategoryIcon(category.name),
                size: 40,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getEnhancedCategoryGradient(String categoryName) {
    // Generate modern, sophisticated gradients based on category name
    final name = categoryName.toLowerCase();

    if (name.contains('فساتين') || name.contains('dress')) {
      return [Color(0xFFFF6B9D), Color(0xFFFF8E9B), Color(0xFFFFC3A0)];
    } else if (name.contains('أحذية') || name.contains('shoes')) {
      return [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFF8B5A9F)];
    } else if (name.contains('حقائب') || name.contains('bags')) {
      return [Color(0xFFf093fb), Color(0xFFf5576c), Color(0xFFFF6B9D)];
    } else if (name.contains('إكسسوارات') || name.contains('accessories')) {
      return [Color(0xFF4facfe), Color(0xFF00f2fe), Color(0xFF43e97b)];
    } else if (name.contains('ملابس') || name.contains('clothes')) {
      return [Color(0xFF43e97b), Color(0xFF38f9d7), Color(0xFF4facfe)];
    } else if (name.contains('جمال') || name.contains('beauty')) {
      return [Color(0xFFffecd2), Color(0xFFfcb69f), Color(0xFFFF8E9B)];
    } else if (name.contains('رياضة') || name.contains('sport')) {
      return [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFF43e97b)];
    } else {
      // Enhanced default gradient
      return [Color(0xFFB76E79), Color(0xFFD4A5A5), Color(0xFFF4C2C2)];
    }
  }

  IconData _getCategoryIcon(String categoryName) {
    final name = categoryName.toLowerCase();

    if (name.contains('فساتين') || name.contains('dress')) {
      return Icons.checkroom;
    } else if (name.contains('أحذية') || name.contains('shoes')) {
      return Icons.sports_soccer; // Closest to shoes
    } else if (name.contains('حقائب') || name.contains('bags')) {
      return Icons.shopping_bag;
    } else if (name.contains('إكسسوارات') || name.contains('accessories')) {
      return Icons.watch;
    } else if (name.contains('ملابس') || name.contains('clothes')) {
      return Icons.checkroom;
    } else {
      return Icons.category;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            'لا توجد فئات مطابقة للبحث',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12),
          Text(
            'جرب البحث بكلمات مختلفة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
