//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/events/login_event.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/services/auth_service.dart';
import '/app/models/libyan_city.dart';

class AccountRegistrationPage extends NyStatefulWidget {
  static RouteView path =
      ("/account-register", (_) => AccountRegistrationPage());

  AccountRegistrationPage({super.key})
      : super(child: () => _AccountRegistrationPageState());
}

class _AccountRegistrationPageState extends NyPage<AccountRegistrationPage> {
  final TextEditingController _tfEmailAddressController =
          TextEditingController(),
      _tfPasswordController = TextEditingController(),
      _tfFirstNameController = TextEditingController(),
      _tfLastNameController = TextEditingController(),
      _tfPhoneNumberController = TextEditingController();

  LibyanCity? _selectedCity;
  List<LibyanCity> _cities = [];

  @override
  get init => () {
    try {
      print('🔄 Initializing AccountRegistrationPage...');
      _cities = LibyanCitiesData.getAllCities();
      print('✅ Loaded ${_cities.length} Libyan cities');
    } catch (e) {
      print('❌ Error initializing AccountRegistrationPage: $e');
    }
  };



  @override
  Widget view(BuildContext context) {
    print('🔄 Building AccountRegistrationPage view...');
    try {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: Icon(Icons.close),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text("إنشاء حساب"), // Arabic for "Create Account"
          centerTitle: true,
        ),
        body: SafeAreaWidget(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  SizedBox(height: 10),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Expanded(
                        child: TextEditingRow(
                          heading: "الاسم الأول", // Arabic for "First Name"
                          controller: _tfFirstNameController,
                          shouldAutoFocus: true,
                          keyboardType: TextInputType.text,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: TextEditingRow(
                          heading: "اللقب", // Arabic for "Last Name"
                          controller: _tfLastNameController,
                          shouldAutoFocus: false,
                          keyboardType: TextInputType.text,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                TextEditingRow(
                  heading: "البريد الإلكتروني", // Arabic for "Email address"
                  controller: _tfEmailAddressController,
                  shouldAutoFocus: false,
                  keyboardType: TextInputType.emailAddress,
                  hintText: "<EMAIL>",
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "ملاحظة البريد الإلكتروني", // Arabic for "Email Notice"
                        style: TextStyle(
                          fontSize: 12,
                          color: ThemeColor.get(context).textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      Text(
                        "يُفضل استخدام Gmail للتواصل المحلي", // Arabic for "Gmail preferred for local communication"
                        style: TextStyle(
                          fontSize: 11,
                          color: ThemeColor.get(context).textMuted,
                        ),
                      ),
                    ],
                  ),
                ),
                TextEditingRow(
                  heading: "رقم الهاتف", // Arabic for "Phone Number"
                  controller: _tfPhoneNumberController,
                  shouldAutoFocus: false,
                  keyboardType: TextInputType.phone,
                  hintText: "+21894xxxxxxx",
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "ملاحظة واتساب", // Arabic for "WhatsApp Notice"
                        style: TextStyle(
                          fontSize: 12,
                          color: ThemeColor.get(context).warningColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        "يجب استخدام الرقم في واتساب لتلقي الإشعارات وتحديثات الطلبات", // Arabic for WhatsApp notice
                        style: TextStyle(
                          fontSize: 11,
                          color: ThemeColor.get(context).warningColor,
                        ),
                      ),
                    ],
                  ),
                ),
                // City dropdown using the existing implementation
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "المدينة", // Arabic for "City"
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          border: Border.all(color: ThemeColor.get(context).borderPrimary),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<LibyanCity>(
                            value: _selectedCity,
                            hint: Text("اختر المدينة"), // Arabic for "Choose City"
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: _cities.map((LibyanCity city) {
                              return DropdownMenuItem<LibyanCity>(
                                value: city,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(city.getDisplayName()),
                                    Text(
                                      city.getFormattedDeliveryCost(),
                                      style: TextStyle(
                                        color: ThemeColor.get(context).textSecondary,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (LibyanCity? newValue) {
                              setState(() {
                                _selectedCity = newValue;
                              });
                            },
                          ),
                        ),
                      ),
                      if (_selectedCity != null)
                        Container(
                          margin: EdgeInsets.only(top: 4),
                          child: Text(
                            "تكلفة التوصيل: ${_selectedCity!.getFormattedDeliveryCost()}", // Arabic for "Delivery cost"
                            style: TextStyle(
                              fontSize: 12,
                              color: ThemeColor.get(context).successColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                TextEditingRow(
                  heading: "كلمة المرور", // Arabic for "Password"
                  controller: _tfPasswordController,
                  shouldAutoFocus: false,
                  obscureText: true,
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10),
                  child: PrimaryButton(
                    title: "إنشاء حساب", // Arabic for "Create Account"
                    isLoading: isLocked('register_user'),
                    action: _signUpTapped,
                  ),
                ),

                  // Terms and conditions
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: InkWell(
                      onTap: _viewTOSModal,
                      child: RichText(
                        text: TextSpan(
                          text:
                              '${trans("By tapping \"Register\" you agree to ")} ${AppHelper.instance.appConfig?.appName ?? "Velvete"}\'s ',
                          children: <TextSpan>[
                            TextSpan(
                                text: trans("terms and conditions"),
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            TextSpan(text: '  ${trans("and")}  '),
                            TextSpan(
                                text: trans("privacy policy"),
                                style: TextStyle(fontWeight: FontWeight.bold)),
                          ],
                          style: TextStyle(
                              color: ThemeColor.get(context).textSecondary),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } catch (e, stackTrace) {
      print('❌ Error building AccountRegistrationPage: $e');
      print('Stack trace: $stackTrace');
      return Scaffold(
        appBar: AppBar(
          title: Text('Registration Error'),
          leading: IconButton(
            icon: Icon(Icons.close),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: ThemeColor.get(context).errorColor),
              SizedBox(height: 16),
              Text('Registration page failed to load'),
              SizedBox(height: 8),
              Text('Error: $e', style: TextStyle(fontSize: 12)),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }
  }

  _signUpTapped() async {
    String email = _tfEmailAddressController.text,
        password = _tfPasswordController.text,
        firstName = _tfFirstNameController.text,
        lastName = _tfLastNameController.text,
        phoneNumber = _tfPhoneNumberController.text;

    if (email.isNotEmpty) {
      email = email.trim();
    }
    if (phoneNumber.isNotEmpty) {
      phoneNumber = phoneNumber.trim();
    }

    // Validation
    if (!isEmail(email)) {
      showToast(
          title: trans("Oops"),
          description: trans("That email address is not valid"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (password.length <= 5) {
      showToast(
          title: trans("Oops"),
          description: trans("Password must be a min 6 characters"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (firstName.isEmpty || lastName.isEmpty) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء إدخال الاسم واللقب", // Arabic for "Please enter first and last name"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (phoneNumber.isEmpty) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء إدخال رقم الهاتف", // Arabic for "Please enter phone number"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (!phoneNumber.startsWith('+218')) {
      showToast(
          title: trans("Oops"),
          description: "رقم الهاتف يجب أن يبدأ بـ +218", // Arabic for "Phone number must start with +218"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (_selectedCity == null) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء اختيار المدينة", // Arabic for "Please select city"
          style: ToastNotificationStyleType.danger);
      return;
    }

    await lockRelease('register_user', perform: () async {
      try {
        // Phase 2.2: Use AuthService for enhanced registration with Libyan market data
        AuthResult result = await AuthService().register(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          phoneNumber: phoneNumber,
          city: _selectedCity!.name,
          cityArabic: _selectedCity!.nameArabic,
          deliveryCost: _selectedCity!.deliveryCost,
        );

        if (result.success) {
          // Show success message and navigate
          showToast(
              title: "مرحباً", // Arabic for "Hello"
              description: "تم إنشاء الحساب بنجاح! مرحباً بك في متجر فيلفيت", // Arabic for "Account created successfully! Welcome to Velvete Store"
              style: ToastNotificationStyleType.success);

          event<LoginEvent>(data: {"email": email});
          Navigator.pushNamedAndRemoveUntil(
              context, "/home", (Route<dynamic> route) => false);
        } else {
          showToast(
              title: trans("Oops!"),
              description: result.message ?? trans("Failed to create account"),
              style: ToastNotificationStyleType.danger);
        }
      } catch (e) {
        print('Registration error: $e');
        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong, please try again"),
            style: ToastNotificationStyleType.danger);
      }
    });
  }

  _viewTOSModal() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(trans("Actions")),
        content: Text(trans("View Terms and Conditions or Privacy policy")),
        actions: <Widget>[
          MaterialButton(
            onPressed: _viewTermsConditions,
            child: Text(trans("Terms and Conditions")),
          ),
          MaterialButton(
            onPressed: _viewPrivacyPolicy,
            child: Text(trans("Privacy Policy")),
          ),
          Divider(),
          TextButton(
            onPressed: pop,
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewTermsConditions() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/terms-conditions/");
  }

  void _viewPrivacyPolicy() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/privicy-policy/");
  }
}
